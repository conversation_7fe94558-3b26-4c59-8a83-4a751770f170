<template>
  <div 
    v-if="isShow && imageUrl" 
    class="ad-banner"
    @click="handleClick"
  >
    <van-image
      :src="imageUrl"
      alt="广告"
      fit="cover"
      width="100"
      height="126"
      :lazy-load="true"
      @error="handleError"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'

// Props
interface Props {
  imageUrl?: string
  linkUrl?: string
  show?: boolean
}

const emit = defineEmits<{
  (e: 'update:show', show: boolean): void
}>()

const isShow = computed({
  get: () => props.show && liveStatus.value === LiveStatus.LIVE,
  set: (val) => emit('update:show', val)
})

const props = withDefaults(defineProps<Props>(), {
  imageUrl: '',
  linkUrl: '',
  show: false
})

// 处理点击
const handleClick = () => {
  if (props.linkUrl) {
    // #TODO 处理广告位点击事件

  }
}

// 处理图片错误
const handleError = () => {
  close()
}

// 暴露关闭方法
const close = () => {
  isShow.value = false
}

onMounted(() => {
  /* 添加监听 */
  window.addEventListener("message", handleMessage);
});
/* 组件卸载时取消监听 */
onUnmounted(() => {
  removeLiveListener();
});

/* 直播状态 */
enum LiveStatus {
  /* 直播中 */
  LIVE = "直播中",
  /* 预告 */
  TRAILER = "预告",
  /* 回放 */
  PLAYBACK = "回放",
  /* 直播结束 */
  ENDED = "结束",
}

let liveStatus = ref("");

/* 直播事件处理函数 */
const handleMessage = (event: MessageEvent) => {
  const rawData = event?.data;
  let data = null;
  try {
    // 优先使用原始数据，为空时兜底空JSON字符串
    const parseTarget = typeof rawData === 'string' ? rawData : '{}';
    data = JSON.parse(parseTarget);
  } catch (e) {
    return; // 解析失败时终止后续逻辑
  }
  if (!data || !data?.action){
    return
  }
  try {
    switch (data.action) {
      case "player.play":
        /* 这里可能会监听到多次 */
        break;
      case "player.destroy":
        removeLiveListener();
        break;
      case "player.status":
        liveStatus.value = data.payload;
        break;
      default:
        return;
    }
  } catch (e) {
  }
};

function removeLiveListener() {
  window.removeEventListener("message", handleMessage);
}
</script>

<style lang="less" scoped>
.ad-banner {
  color: red;
  position: fixed;
  top: 60px;
  right: 12px;
  width: 100px;
  height: 126px;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  
  :deep(.van-image) {
    width: 100%;
    height: 100%;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .ad-banner {
    width: 90px;
    height: 113px;
  }
}

@media (max-width: 320px) {
  .ad-banner {
    width: 80px;
    height: 100px;
  }
}
</style>
