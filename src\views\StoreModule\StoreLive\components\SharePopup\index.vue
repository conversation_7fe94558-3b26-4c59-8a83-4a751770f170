<template>
  <van-popup
    v-model:show="showRef"
    position="bottom"
    round
    :closeable="false"
    style="height: auto"
    safe-area-inset-bottom
    @close="closeFn"
  >
    <div class="share-popup-content">
      <div class="share-list">
        <div class="share-item" v-for="item, index in shareList" :key="index" @click="handleShare(item.type)" >
          <img :src="item.icon" :alt="item.name" />
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="cancel-btn">
        <van-button type="primary" color="#FFFFFF" style="color: black;" block @click="handleClose">取消</van-button>
      </div>
    </div>
    <img :src="qrCodeDataUrl" v-if="isQrCodeShare" class="qr-code" alt="" >

  </van-popup>
    <img :src="shareBg" v-if="isWeChatShare" class="share-bg" alt="" @click="isWeChatShare = false" >
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import weChat from '@/assets/storeImage/live/weChat.png'
import qrCode from '@/assets/storeImage/live/qrCode.png'
import link from '@/assets/storeImage/live/link.png'
import shareBg from '@/assets/storeImage/live/shareBg.png'
import { copyText } from '@/utils/clipboardUtils'
import { useMessages } from "@/hooks/useMessage";
const { createMessageError, createMessageSuccess } = useMessages();
import QRcode from "qrcode";
import {
  receiveShareCoupon,
} from "@/services/storeApi";
import useShare from "../../hooks/useShare";
const { shareCouponData, getShareCouponData, handleReceiveShareCoupon } = useShare();

const shareList = [
    {
        name: '微信好友',
        icon: weChat,
        type: 'weChat'
    },
    {
        name: '复制链接',
        icon: link,
        type: 'copyLink'
    },
    {
        name: '二维码',
        icon: qrCode,
        type: 'qrCode'
    }
]

const props = defineProps<{
    show: boolean;
    liveRoomId: number | string;
}>()

const emit = defineEmits<{
    (e: 'update:show', show: boolean): void
}>()

const showRef = computed({
    get: () => props.show,
    set: (val) => emit('update:show', val)
})

const handleClose = () => {
    showRef.value = false
}

const isWeChatShare = ref(false)

const handleShare = async (type: string) => {
    switch (type) {
        case 'weChat':
            isWeChatShare.value = true;
            showRef.value = false;
            break;
        case 'copyLink':
            await copyText(window.location.href);
            createMessageSuccess('复制链接成功')
            break;
        case 'qrCode':
            generateQRCode(window.location.href)
            break;
        default:
            break;
    }
    handleReceiveShareCoupon(props.liveRoomId)
}

const closeFn = () => {
    isQrCodeShare.value = false;
}

const isQrCodeShare = ref(false)
const qrCodeDataUrl = ref('')
/** 生成二维码 */
  async function generateQRCode(url: string): Promise<string | null> {
    try {
      qrCodeDataUrl.value = await QRcode.toDataURL(url, {
        width: 156,
        height: 156,
        margin: 2,
      });
      isQrCodeShare.value = true;
    } catch (err) {
      createMessageError("生成二维码失败：" + err);
      return null;
    }
  }
</script>

<style scoped lang="less">
.share-popup-content{
    background: #F8F8F8;
    .share-list{
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 20px;
        padding: 20px 48px;
        .share-item{
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            img{
                width: 48px;
                height: 48px;
            }
            span{
                font-size: 12px;
                color: #999999;
            }
        }
    }
    .cancel-btn{
        padding: 20px;
    }

}

.qr-code{
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    z-index: 999;
}

.share-bg{
    position: fixed;
    top: 52px;
    left: 0;
    width: 100%;
    height: calc(100vh - 38px);
    z-index: 999;
}
</style>
