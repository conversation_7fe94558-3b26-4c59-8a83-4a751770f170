<template>
    <JLoadingWrapper :show="isLoading || goodLoading">
        <div class="goods-box">
            <div class="goods-content">
                <van-swipe class="content-swiper" ref="swiperRef" @change="handleSwiperChange" :loop="false"
                    indicator-color="#4DA4FF">
                    <van-swipe-item v-for="(item, index) in productImgDTOList" :key="item.id">
                        <div class="swiper-item">
                            <van-image v-if="item.type == MediaTypeEnum.Image" fit="contain"
                                @click="handlePreview(index, item.type)" width="100%" height="100%" :src="item.path" />
                            <GoodsVideo ref='goodsVideoRef' :autoplay="true" :src="item.path"
                                v-if="item.type == MediaTypeEnum.Video" @click-mask="handlePreview(index, item.type)"
                                :poster="firstImg"></GoodsVideo>
                        </div>
                    </van-swipe-item>
                </van-swipe>
                <div class="content-container">
                    <!-- 福利卷 -->
                    <div class="welfare-warp" v-if="props.type == StoreGoodsEnum.WelfareTicket">
                        <div class="welfare-header">
                            <div class="header-left">
                                <div class="left-count">{{ curGoodsInfo.exchangeCount }}件</div>
                                <div class="left-name">{{ curGoodsInfo.couponCateName }}</div>
                            </div>
                            <div class="header-right">
                                {{ initSaled }}
                            </div>
                        </div>
                        <div class="welfare-content">
                            <GoodsTitle :style="{ 'fontSize': '18px' }" :state="tempGoodsInfo" />
                        </div>
                    </div>
                    <template v-else>
                        <div class="activity-warp" v-if="isExistActivePrice">
                            <div class="header-warp">
                                <div class="warp-price">
                                    <div class="price-content">
                                        <div class="content-info">
                                            <div class="info-label">
                                                <span>活动价</span>
                                            </div>
                                            <PriceContent 
                                                :price="curGoodsInfo.activityPrice || 0"
                                                style="color: #fff;font-weight:500;" 
                                                :price-pre-font-size="16"
                                                :price-font-size="26" 
                                            />
                                        </div>
                                        <div class="content-rectangle">
                                            <div class="rectangle-price">
                                                <span>可省</span>
                                                <span>￥</span>
                                                <span>{{ discountPrice }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="price-content">
                                        <div class="content-info">
                                            <div class="info-label">
                                                <span>日常价</span>
                                            </div>
                                            <PriceContent 
                                                :price="curGoodsInfo.price || 0"
                                                :marketPrice="curGoodsInfo.marketPrice || 0"
                                                style="color: #fff;font-weight:500;" 
                                                :price-pre-font-size="10"
                                                :price-font-size="16" 
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div class="warp-sold">{{ initSaled }}</div>
                            </div>
                            <div class="content-banner footer-warp">
                                <div class="banner-desc">
                                    <GoodsTitle :style="{ 'fontSize': '14px' }" :state="tempGoodsInfo" />
                                </div>
                                <div class="tag-list" v-if="props.type == StoreGoodsEnum.Goods">
                                    <!-- <template v-for="(item, index) in expressTagList" :key="item.id">
                                        <div @click="showExpressTip = true" v-if="item.isShow">
                                            <TagBtn :active="true" :style="customTagStyle">
                                                {{ item.name }}
                                            </TagBtn>
                                        </div>
                                    </template> -->
                                    <TagBtn :active="true" type="danger" style="margin-top: 8px;" v-if="!isNullOrUnDef(curGoodsInfo.points)"
                                        :style="customTagStyle">
                                        可获得{{ curGoodsInfo.points }}积分
                                    </TagBtn>
                                </div>
                            </div>
                        </div>
                        <div class="content-banner" v-else>
                            <div class="banner-header">
                                <div class="banner-price">
                                    <IntegralContent :sku-info="curGoodsInfo"
                                        v-if="props.type == StoreGoodsEnum.IntegralGoods">
                                    </IntegralContent>
                                    <PriceContent 
                                        v-else-if="!isExistActivePrice"
                                        :sku-info="curGoodsInfo" 
                                        :marketPrice="curGoodsInfo.marketPrice || 0" 
                                        :price-pre-font-size="16" 
                                        :price-font-size="26" 
                                    />
                                </div>
                                <div class="sold" v-if="!isExistActivePrice">
                                    {{ initSaled }}
                                </div>
                            </div>
                            <div class="banner-desc">
                                <GoodsTitle :style="{ 'fontSize': '14px' }" :state="tempGoodsInfo" />
                            </div>
                            <div class="tag-list" v-if="props.type == StoreGoodsEnum.Goods">
                                <!-- <template v-for="(item, index) in expressTagList" :key="item.id">
                                    <div @click="showExpressTip = true" v-if="item.isShow">
                                        <TagBtn :active="true" :style="customTagStyle">
                                            {{ item.name }}
                                        </TagBtn>
                                    </div>
                                </template> -->
                                <TagBtn :active="true" type="danger" style="margin-top: 8px;"  v-if="!isNullOrUnDef(curGoodsInfo.points)"
                                    :style="customTagStyle">
                                    可获得{{ curGoodsInfo.points }}积分
                                </TagBtn>
                            </div>
                        </div>
                    </template>
                    <div class="container-warp" :style="containerStyle">
                        <BannerContainer style="margin-bottom:10px;" :is-show-title="false">
                            <div class="banner-sku" @click="handleShowModal">
                                <div class="sku-left">
                                    <div class="label">规格</div>
                                    <div class="content">
                                        已选：{{ curGoodsInfo.name || curGoodsInfo.specName }}
                                    </div>
                                </div>
                                <div class="sku-right">
                                    <van-icon name="arrow" />
                                </div>
                            </div>
                            <div class="store-box" @click="handleStoreClick" v-if="props.type != StoreGoodsEnum.IntegralGoods">
                                <img :src="storeInfo.storeAvatar || storeSrc" class="store-img">
                                <div class="store-content">
                                    <div class="content-title">{{ storeInfo.storeName }}</div>
                                    <div class="content-desc">
                                        <div class="desc-name">进店看看</div>
                                        <van-icon name="arrow" />
                                    </div>
                                </div>
                            </div>
                        </BannerContainer>
                        <BannerContainer v-if="curType == GoodsTypeEnum.OTC_DRUG && tempGoodsInfo.isPres == 0"
                            custom-style="margin-bottom:10px;" :is-show-line="false" title="说明书">
                            <MedicinalExplian :state="productInsertsDTO"></MedicinalExplian>
                        </BannerContainer>
                        <BannerContainer title="详情" :is-show-line="false" v-if="tempGoodsInfo.desc || tempGoodsInfo.description">
                            <div class="info-rich">
                                <RichText :value="tempGoodsInfo.desc || tempGoodsInfo.description"></RichText>
                            </div>
                        </BannerContainer>
                    </div>
                </div>
            </div>
            <div class="footer-box">
                <van-button class="btn" round @click="handleShowModal" :disabled="false">{{ btnText }}</van-button>
            </div>
        </div>
        <!-- 物流 -->
        <ExpressTipModal v-model:show="showExpressTip" />
        <!-- 商品规格 -->
        <GoodsSkuModal v-model:show="showSku" v-model:sku-id="curSku" :isWatchGoods="false"
            :is-show-init-cart-count="true" :state="tempGoodsInfo" />
        <!-- 积分商品规格 -->
        <IntegralModal v-model:show="showIntegralModal" v-model:sku-id="curSku" :state='tempGoodsInfo'>
        </IntegralModal>
        <!-- 福利卷 -->
        <welfareModal v-model:show="showWelfareModal" v-model:sku-id="curSku" :state='tempGoodsInfo'>
        </welfareModal>
        <!-- 图片视频预览 -->
        <MediaPreview v-model:show="showPreview" :previewList="previewList" v-model:type="curSwiperStatus.type"
            :videoPlayStatus="videoPlayStatus" v-model:currentIndex="curSwiperStatus.currentIndex"
            @closePreview="closePreview" />
    </JLoadingWrapper>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, type StyleValue } from "vue"
import { showImagePreview, showToast } from "vant";
import storeSrc from "@/assets/storeImage/storeMine/store.png";
import { isNullOrUnDef, isString } from "@/utils/isUtils";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import GoodsSkuModal from "../components/GoodsSkuModal/index.vue"
import IntegralModal from "../components/IntegralModal/index.vue"
import GoodsTitle from "../components/GoodsTitle.vue"
import GoodsVideo from "./components/GoodsVideo.vue";
import MediaPreview from "./components/MediaPreview/index.vue";
import PriceContent from "../components/PriceContent.vue"
import IntegralContent from "../components/IntegralContent.vue"
import welfareModal from "../components/welfareModal/index.vue";
import BannerContainer from "../components/BannerContainer/index.vue";
import ExpressTipModal from "./components/ExpressTipModal.vue";
import MedicinalExplian from "./components/MedicinalExplian.vue";
import { MediaTypeEnum } from "@/enums/storeGoods";
import RichText from "@/components/RichText/index.vue";
import TagBtn from "../components/TagBtn/index.vue";
import { RoutesName } from "@/enums/routes";
import { useRoute,useRouter } from "vue-router";
import { routesMap } from '@/router/maps';
import { GoodsTypeEnum, StoreGoodsEnum } from "@/enums/storeGoods";
import { useGoodsInfo, useCureInfo, useCart, usePreview } from "./hooks"
import { useUserRole } from "@/views/StoreModule/hooks";

const route = useRoute()
const router = useRouter()
const { storeId } = useUserRole();
const props = withDefaults(defineProps<{
    id: string,
    type: StoreGoodsEnum,
    liveId: string,
}>(), {
    id: '',
    type: StoreGoodsEnum.Goods,
    liveId: ''
})
const { tempGoodsInfo, goodLoading, initSaled, showSku, skuInfo, detailId,getStoreInfoByStoreId, showIntegralModal, showWelfareModal,
    productInsertsDTO, productImgDTOList, isInitSku, curType, customStyleTag, getDetail, firstImg, storeInfo } = useGoodsInfo(props.type)
const { isLoading, curSku, curGoodsInfo, discountPrice, isExistActivePrice, setMinSkuData } = useCart({ goodsInfo: tempGoodsInfo, type: props.type, isWatchGoods: false })
const { tagList, expressTagList, showExpressTip, customTagStyle } = useCureInfo(tempGoodsInfo, curGoodsInfo)
const { showPreview, handleSwiperChange, handlePreview, playVideo, goodsVideoRef, swiperRef, previewList, curSwiperStatus, videoPlayStatus, closePreview } = usePreview(productImgDTOList)
const containerStyle = computed<StyleValue>(() => {
    let result: StyleValue = {};
    if (isExistActivePrice.value) {
        result = {
            marginTop: '-8px',
            position: 'relative',
        }
    }
    return result;
});
const btnText = computed(() => {
    return props.type == StoreGoodsEnum.Goods ? '立即购买' : '立即兑换'
})
const handleShowModal = () => {
    // 判断是否归属门店
    if(!storeId.value && props.liveId) {
        showToast('您当前无归属门店，无法购买!');
        return;
    }
    switch (Number(props.type)) {
        case StoreGoodsEnum.Goods:
            showSku.value = true
            break;
        case StoreGoodsEnum.WelfareTicket:
            showWelfareModal.value = true
            break;
        case StoreGoodsEnum.IntegralGoods:
            showIntegralModal.value = true
            break;
    }
}
const handlePreviewImage = (index: number) => {
    showImagePreview({
        images: productImgDTOList.value.map(item => item.path),
        startPosition: index
    })
}
  const handleStoreClick = () => {
    router.push(routesMap[RoutesName.StoreHome]);
  };
//监听id变化
watch(() => props.id, (newVal) => {
    detailId.value = newVal
    getStoreInfoByStoreId()
    getDetail(() => {
        if (!isInitSku.value) {
            return
        }
        // setSkuInfo(val?.specId)
        curSku.value = ''
        setMinSkuData()
    })
}, {
    immediate: true
})
</script>

<style scoped lang="less">
@import "@/styles/storeVar.less";

:deep .header-title-container {
    margin: 4px 0 10px 0 !important;
}

.shelve {
    filter: opacity(50%);
}

.goods-box {
    background-color: #f8f8f8;
    width: 100%;
    padding-bottom: calc(60px + constant(safe-area-inset-bottom));
    padding-bottom: calc(60px + env(safe-area-inset-bottom));

    .goods-content {
        .content-swiper {
            height: 340px;

            .swiper-item {
                width: 100%;
                height: 100%;

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .content-container {
            .welfare-warp {
                .welfare-header {
                    display: flex;
                    .header-left {
                        flex: 1;
                        padding: 10px 16px;
                        background-repeat: no-repeat;
                        display: flex;
                        align-items: center;
                        background-size: 100% 100%;
                        background-image: url('@/assets/storeImage/product/welfarebg.png');
                        font-size: 16px;
                        color: #fff;
                        font-weight: bold;
                        display: flex;
                        align-items: baseline;

                        .left-count {
                            margin-right: 5px;
                            font-size: 22px;
                        }
                    }

                    .header-right {
                        color: @error-color;
                        display: flex;
                        padding: 8px 12px;
                        align-items: center;
                        justify-content: center;
                        background-color: #FFE1E0;
                        max-width: 80px;
                    }

                }
                .welfare-content {
                    padding: 8px 12px;
                    background-color: #fff;
                }
            }

            .activity-warp {
                width: 100%;
                position: relative;
                box-sizing: border-box;
                padding: 8px;
                color: #fff;
                font-size: 12px;
                border-radius: 12px;
                background-size: 100% 100%;
                background-image: url('@/assets/storeImage/product/activity.png');

                .header-warp {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 4px 12px;

                    .warp-price {
                        z-index: 1;

                        .price-content {
                            display: flex;

                            .content-info {
                                display: flex;
                                align-items: baseline;
                                margin-right: 4px;

                                .info-label {
                                    font-size: 12px;
                                }

                                .info-price {
                                    font-size: 26px;
                                }
                            }

                            .content-rectangle {
                                box-sizing: border-box;
                                position: relative;
                                display: flex;
                                align-items: center;
                                background-size: 100% 100%;
                                background-image: url('@/assets/storeImage/product/rectangle.png');

                                img {
                                    width: 100%;
                                    height: 24px;
                                }

                                .rectangle-price {
                                    padding: 5px;
                                    width: 100%;
                                    height: 100%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 12px;
                                    color: #FF4D00;
                                }
                            }
                        }
                    }

                    .warp-sold {
                        color: #fff;
                        font-size: 14px;
                        word-break: break-all;
                        white-space: nowrap;
                    }
                }


                .footer-warp {
                    background: linear-gradient(180deg, #FFE6CB 0%, #FFFFFF 32%);
                    box-shadow: inset 0 1px 0 0 #FFFFFF;
                    border-radius: 12px;
                    color: #333333;
                    padding: 12px;
                    margin-top: 8px;
                }
            }

            .content-banner {
                width: 100%;
                padding: 12px 12px 0 12px;
                background: linear-gradient(180deg, #FFFFFF 41%, rgba(255, 255, 255, 0) 100%);
                box-sizing: border-box;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;

                .price-tag {
                    color: #fff;
                    padding: 2px 5px;
                    border-radius: 2px;
                    font-size: 8px;
                    margin-right: 4px;
                }

                .banner-header {
                    margin-bottom: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    font-size: 12px;

                    .banner-price {
                        display: flex;
                        align-items: center;
                    }

                    .sold {
                        max-width: 200px;
                        color: #666666;
                    }
                }

                .banner-desc {
                    display: flex;
                    align-items: center;
                }

                .banner-durp {
                    display: flex;
                    align-items: center;
                    color: #666;
                    font-size: 12px;
                    margin-bottom: 10px;
                }

                .banner-sku {
                    margin: 10px 0;
                    display: flex;
                    align-items: center;

                    .sku-title {
                        font-size: 12px;
                        color: #666666;
                    }

                    .line {
                        margin: 0 10px;
                        width: 2.5px;
                        height: 15px;
                        background-color: #EEEEEE;
                    }

                    .sku-list {
                        flex: 1;
                        overflow: auto;
                        display: flex;
                        align-items: center;

                        .sku-item {
                            margin-right: 10px;
                        }
                    }

                    .sku-right {
                        padding-left: 10px;
                    }
                }

                .tag-list {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 4px;
                }
            }

            .container-warp {
                padding: 12px;

                .banner-sku {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .sku-list {
                        flex: 1;
                        overflow: auto;
                        display: flex;
                        align-items: center;

                        .sku-item {
                            margin-right: 10px;
                        }
                    }

                    .sku-left {
                        flex: 1;
                        display: flex;
                        align-items: center;
                        font-size: 12px;
                        overflow: hidden;

                        .label {
                            color: #666666;
                            margin-right: 12px;
                        }

                        .content {
                            flex: 1;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }

                    .sku-right {
                        padding-left: 10px;
                    }
                }

                .store-box {
                    margin-top: 9px;
                    padding: 12px;
                    display: flex;
                    align-items: center;
                    background-color: #F8F8F8;
                    border-radius: 8px;
                    cursor: pointer;

                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 4px;
                        margin-right: 8px;
                    }

                    .store-content {
                        flex: 1;
                        height: 40px;
                        overflow: hidden;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;

                        .content-title {
                            font-size: 14px;
                            font-weight: 500;
                            margin-bottom: 2px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        .content-desc {
                            display: flex;
                            align-items: center;
                            font-size: 12px;
                            color: #999999;

                            .desc-name {
                                margin-right: 4px;
                            }
                        }
                    }
                }
            }


            .content-banner {
                width: 100%;
                box-sizing: border-box;

                .banner-header {
                    margin-bottom: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    font-size: 12px;

                    .banner-price {
                        display: flex;
                        align-items: center;

                    }

                    .sold {
                        max-width: 200px;
                        color: #666666;
                    }
                }

                .banner-desc {
                    display: flex;
                    align-items: center;
                }

                .banner-durp {
                    display: flex;
                    align-items: center;
                    color: #666;
                    font-size: 12px;
                    margin-bottom: 10px;
                }

                .tag-list {
                    display: flex;
                    flex-wrap: wrap;
                }
            }

            .info-rich {

                .rich-tip {
                    font-size: 12px;
                }

                .rich-content {
                    width: 100%;
                    margin-top: 10px;

                    img {
                        width: 100%;
                    }
                }
            }
        }
    }

    .footer-box {
        position: fixed;
        left: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100vw;
        padding: 8px 12px;
        padding-bottom: calc(8px + constant(safe-area-inset-bottom));
        padding-bottom: calc(8px + env(safe-area-inset-bottom));
        box-sizing: border-box;
        background-color: #fff;
        border-top: 2px solid #FFEDE5;

        .btn {
            width: 100%;
            background: @error-color;
            color: #fff;
        }
    }
}

:deep(.van-swipe__indicator) {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    width: 14px;
}

:deep(.rich-text-wrapper img) {
    width: 100%;
}

:deep(.rich-text-wrapper video) {
    max-width: 100%;
}
</style>
