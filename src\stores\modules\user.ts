import { defineStore } from "pinia";
import { StoreName } from "@/enums/stores";
import { stores } from "@/stores";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { isStoreMode } from "@/utils/envUtils";

export const useUserStore = defineStore(StoreName.User, {
  state: () => {
    return {
      _userInfo: null,
      _token: null,
      _routeConfig: null,
      _redirectUrl:null,
      _storeToken:null,
      _storeUserInfo: null,
      // 直播已领取分享券缓存
      _liveCouponReceivedCache: {},
    };
  },
  getters: {
    redirectUrl: state => {
      return state._redirectUrl;
    },
    userInfo: state => {
      if (!state._userInfo) {
        try {
          const userConfigStorage = createCacheStorage(CacheConfig.UserInfo);
          const _userInfoCache = userConfigStorage.get();
          state._userInfo = _userInfoCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._userInfo;
    },
    storeUserInfo: state => {
      if (!state._storeUserInfo) {
        try {
          const userConfigStorage = createCacheStorage(CacheConfig.StoreUserInfo);
          const _userInfoCache = userConfigStorage.get();
          state._storeUserInfo = _userInfoCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._storeUserInfo;
    },
    token: state => {
      if (!state._token) {
        try {
          const authStorage = createCacheStorage(CacheConfig.Token);
          const _tokenCache = authStorage.get('value');
          state._token = _tokenCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._token;
    },
     storeToken: state => {
      if (!state._storeToken) {
        try {
          const authStorage = createCacheStorage(CacheConfig.StoreToken);
          const _tokenCache = authStorage.get('value');
          state._storeToken = _tokenCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._storeToken;
    },
    routeConfig: state => {
      if (!state._routeConfig) {
        try {
          const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
          const _routeConfigCache = routeConfigStorage.get(isStoreMode()?'st':'sg');
          state._routeConfig = _routeConfigCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._routeConfig;
    },
    liveCouponReceivedCache: state => {
      if (!state._liveCouponReceivedCache) {
        try {
          const liveCouponReceivedCache = createCacheStorage(CacheConfig.LiveCouponReceivedCache);
          const _liveCouponReceivedCache = liveCouponReceivedCache.get();
          state._liveCouponReceivedCache = _liveCouponReceivedCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._liveCouponReceivedCache;
    }
  },
  actions: {
    setRedirectUrl(url: string) {
      this._redirectUrl = url;
    },
    setUserInfo(userInfo: object) {
      const userConfigStorage = createCacheStorage(CacheConfig.UserInfo);
      userConfigStorage.set(userInfo);
      this._userInfo = userInfo;
    },
    setStoreUserInfo(userInfo: object) {
      const userConfigStorage = createCacheStorage(CacheConfig.StoreUserInfo);
      userConfigStorage.set(userInfo);
      this._storeUserInfo = userInfo;
    },
    setToken(token: string,wxappid:string) {
      this._token = token;
      const authStorage = createCacheStorage(CacheConfig.Token);
      authStorage.set(token,'value');
      authStorage.set(wxappid,'wxappId');
    },
    setStoreToken(token: string,wxappid:string) {
      this._storeToken = token;
      const authStorage = createCacheStorage(CacheConfig.StoreToken);
      authStorage.set(token,'value');
      authStorage.set(wxappid,'wxappId');
    },
    setRouteConfig(routeConfig: unknown[]) {
      this._routeConfig = routeConfig;
      const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
      routeConfigStorage.set(routeConfig,isStoreMode()?'st':'sg');
    },
    clearLoginStatus(){
      this._userInfo = null;
      this._token = null;
      // this._routeConfig = null;
      const tokenStorage = createCacheStorage(CacheConfig.Token);
      tokenStorage.remove()
      const userConfigStorage = createCacheStorage(CacheConfig.UserInfo);
      userConfigStorage.remove();
    },
    setLiveCouponReceivedCache(liveCouponReceivedCache: object) {
      const liveCouponReceivedCacheStorage = createCacheStorage(CacheConfig.LiveCouponReceivedCache);
      liveCouponReceivedCacheStorage.set(liveCouponReceivedCache);
      this._liveCouponReceivedCache = liveCouponReceivedCache;
    }
  },
});

export function useUserStoreWithoutSetup() {
  return useUserStore(stores);
}
