import {
  getLiveWatchTimeCouponList,
  getShareCoupon,
  type ListWatchTimeCouponResponse,
  type LiveCouponInfoResponse,
  receiveCoupon,
  receiveShareCoupon,
  type receiveCouponVo,
} from "@/services/storeApi";
import { ref } from "vue";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
const userStore = useUserStoreWithoutSetup();


// 分享券
const shareCouponData = ref<any>({});

export default function useShare() {
  /** 获取分享券 */
  async function getShareCouponData(liveRoomId) {
    // 如果缓存中有对应的直播间id 则不再请求接口，直接返回
    console.log(userStore.liveCouponReceivedCache,'userStore.liveCouponReceivedCache');
    
    if (userStore.liveCouponReceivedCache && userStore.liveCouponReceivedCache[liveRoomId]) {
      shareCouponData.value = userStore.liveCouponReceivedCache[liveRoomId];
      return;
    }

    try {
      let result = await getShareCoupon(liveRoomId);
      shareCouponData.value = result;
      if (result.receiveFlag != 0) {
        console.log("已领取");
        
        // 更新缓存
        userStore.liveCouponReceivedCache[liveRoomId] = result;
        userStore.setLiveCouponReceivedCache(userStore.liveCouponReceivedCache);
      }
    } catch (err) {
      console.log(`获取分享券数据失败:${err}`);
      throw new Error("获取分享券数据失败");
    }
  }
  
  /** 领取分享券 */
  async function handleReceiveShareCoupon(liveRoomId) {
    if(shareCouponData.value.receiveFlag != 0) return;
    try {
      const params = {
        liveRoomId: liveRoomId,
        couponBatchId: shareCouponData.value.couponBatchId,
      };
      await receiveShareCoupon(params);
      shareCouponData.value.receiveFlag = 1;
      // 更新缓存
      userStore.liveCouponReceivedCache[liveRoomId] = shareCouponData.value;
      userStore.setLiveCouponReceivedCache(userStore.liveCouponReceivedCache);
    } catch (err) {
      console.log(`领取分享券失败:${err}`);
      throw new Error("领取分享券失败");
    }
  }

  return {
    shareCouponData,
    getShareCouponData,
    handleReceiveShareCoupon
  };
}
